import React, { useState, useMemo, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";
import { Button } from "@/components/ui/button";
import { useSerial, ForcePlate, ForceData } from '@/context/SerialContext';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from 'recharts';
import { ArrowUp, ArrowDown, Maximize2 } from 'lucide-react';

// Maximum number of data points to show in charts
const MAX_DISPLAY_POINTS = 100;

// Define chart colors for consistency
const CHART_COLORS = [
  "#0EA5E9", // blue
  "#9b87f5", // purple
  "#F97316", // orange
  "#8B5CF6", // violet
  "#D946EF", // magenta
  "#10B981", // emerald
];

// Time window to display (ms)
const DEFAULT_TIME_WINDOW = 5000;

// Signal definition
type SignalConfig = {
  plateId: string;
  channel: 'fx' | 'fy' | 'fz' | 'cx' | 'cy';
  visible: boolean;
  color: string;
  label: string;
}

// Plot definition
type PlotConfig = {
  id: string;
  title: string;
  signals: SignalConfig[];
  showSum: boolean;
  yRange: { min: number | null; max: number | null };
  fitToHistory: boolean;
}

const DEFAULT_PLOTS: PlotConfig[] = [
  {
    id: 'fx-plot',
    title: 'Fx',
    signals: [], // Will be populated based on connected plates
    showSum: true,
    yRange: { min: null, max: null },
    fitToHistory: false,
  },
  {
    id: 'fy-plot',
    title: 'Fy',
    signals: [], // Will be populated based on connected plates
    showSum: true,
    yRange: { min: null, max: null },
    fitToHistory: false,
  },
  {
    id: 'fz-plot',
    title: 'Fz',
    signals: [], // Will be populated based on connected plates
    showSum: true,
    yRange: { min: null, max: null },
    fitToHistory: false,
  }
];

const ForcePlot: React.FC<{
  plot: PlotConfig;
  forcePlates: ForcePlate[];
  updatePlot: (id: string, changes: Partial<PlotConfig>) => void;
}> = ({ plot, forcePlates, updatePlot }) => {
  const [draggingY, setDraggingY] = useState(false);
  const [dragStartY, setDragStartY] = useState(0);
  const [dragStartRange, setDragStartRange] = useState({ min: 0, max: 0 });
  
  // Prepare the data for the chart with optimized memoization
  const chartData = useMemo(() => {
    const now = Date.now();
    const allData: Record<string, ForceData[]> = {};

    // Only process plates that have visible signals in this plot
    const relevantPlateIds = plot.signals.filter(s => s.visible).map(s => s.plateId);
    const relevantPlates = forcePlates.filter(plate => relevantPlateIds.includes(plate.id));

    relevantPlates.forEach(plate => {
      if (plate.data.length > 0) {
        // Get data from the last MAX_DISPLAY_POINTS or within time window, whichever is less
        const cutoffTime = now - DEFAULT_TIME_WINDOW;
        const recentData = plate.data.filter(d => d.timestamp > cutoffTime)
          .slice(-MAX_DISPLAY_POINTS);
        allData[plate.id] = recentData;
      }
    });

    // Early return if no data
    if (Object.keys(allData).length === 0) {
      return [];
    }

    // Compile all timestamps across all plates
    const allTimestamps = new Set<number>();
    Object.values(allData).forEach(dataArray => {
      dataArray.forEach(d => allTimestamps.add(d.timestamp));
    });

    // Sort timestamps
    const sortedTimestamps = Array.from(allTimestamps).sort();

    // Convert to chart format
    return sortedTimestamps.map(timestamp => {
      const point: Record<string, number | string> = { timestamp };

      // Add individual signals
      plot.signals.forEach(signal => {
        if (signal.visible && allData[signal.plateId]) {
          const dataPoint = allData[signal.plateId].find(d => d.timestamp === timestamp);
          if (dataPoint) {
            point[signal.plateId + "-" + signal.channel] = dataPoint[signal.channel];
          }
        }
      });

      // Add sum if enabled
      if (plot.showSum && plot.signals.some(s => s.visible)) {
        let sum = 0;
        let count = 0;

        plot.signals.forEach(signal => {
          if (signal.visible && allData[signal.plateId]) {
            const dataPoint = allData[signal.plateId].find(d => d.timestamp === timestamp);
            if (dataPoint) {
              sum += dataPoint[signal.channel];
              count++;
            }
          }
        });

        if (count > 0) {
          point["sum"] = sum;
        }
      }

      return point;
    });
  }, [forcePlates, plot.signals, plot.showSum]);
  
  // Calculate Y-axis domain
  const yDomain = useMemo(() => {
    if (plot.yRange.min !== null && plot.yRange.max !== null) {
      return [plot.yRange.min, plot.yRange.max];
    }

    let dataToAnalyze = chartData;

    if (plot.fitToHistory) {
      // Use all available historical data for fit-to-history mode
      const allHistoricalData: Record<string, ForceData[]> = {};
      const relevantPlateIds = plot.signals.filter(s => s.visible).map(s => s.plateId);
      const relevantPlates = forcePlates.filter(plate => relevantPlateIds.includes(plate.id));

      relevantPlates.forEach(plate => {
        if (plate.data.length > 0) {
          allHistoricalData[plate.id] = plate.data;
        }
      });

      // Convert historical data to chart format for analysis
      const allTimestamps = new Set<number>();
      Object.values(allHistoricalData).forEach(dataArray => {
        dataArray.forEach(d => allTimestamps.add(d.timestamp));
      });

      const sortedTimestamps = Array.from(allTimestamps).sort();
      dataToAnalyze = sortedTimestamps.map(timestamp => {
        const point: Record<string, number | string> = { timestamp };
        plot.signals.forEach(signal => {
          if (signal.visible && allHistoricalData[signal.plateId]) {
            const dataPoint = allHistoricalData[signal.plateId].find(d => d.timestamp === timestamp);
            if (dataPoint) {
              point[signal.plateId + "-" + signal.channel] = dataPoint[signal.channel];
            }
          }
        });
        return point;
      });
    }

    // Check if we have any actual data
    if (dataToAnalyze.length === 0) {
      return [-10, 10]; // Clean range around 0 when no data
    }

    // Calculate min/max from the selected data
    let min = Number.MAX_SAFE_INTEGER;
    let max = Number.MIN_SAFE_INTEGER;
    let hasValidData = false;

    dataToAnalyze.forEach(point => {
      Object.entries(point).forEach(([key, value]) => {
        if (key !== 'timestamp' && typeof value === 'number') {
          min = Math.min(min, value);
          max = Math.max(max, value);
          hasValidData = true;
        }
      });
    });

    // If no valid numeric data found, show clean range around 0
    if (!hasValidData) {
      return [-10, 10];
    }

    // Add padding
    const padding = Math.max(10, (max - min) * 0.1);
    min = Math.floor(min - padding);
    max = Math.ceil(max + padding);

    // Handle edge case where min equals max
    if (min === max) {
      min -= 5;
      max += 5;
    }

    return [min, max];
  }, [chartData, plot.yRange, plot.fitToHistory, forcePlates, plot.signals]);
  
  // Handle Y-axis zoom/pan via drag
  const handleMouseDown = (e: React.MouseEvent) => {
    setDraggingY(true);
    setDragStartY(e.clientY);
    setDragStartRange({ 
      min: yDomain[0] as number, 
      max: yDomain[1] as number 
    });
  };
  
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!draggingY) return;
    
    const deltaY = e.clientY - dragStartY;
    const rangeDelta = ((dragStartRange.max - dragStartRange.min) * deltaY) / 200;
    
    if (e.shiftKey) {
      // Pan operation
      updatePlot(plot.id, {
        yRange: {
          min: dragStartRange.min + rangeDelta,
          max: dragStartRange.max + rangeDelta
        }
      });
    } else {
      // Zoom operation - adjust range around center
      const center = (dragStartRange.min + dragStartRange.max) / 2;
      const newRange = (dragStartRange.max - dragStartRange.min) * (1 + deltaY / 100);
      updatePlot(plot.id, {
        yRange: {
          min: center - newRange / 2,
          max: center + newRange / 2
        }
      });
    }
  };
  
  const handleMouseUp = () => {
    setDraggingY(false);
  };
  
  const toggleFitMode = () => {
    updatePlot(plot.id, {
      fitToHistory: !plot.fitToHistory,
      yRange: { min: null, max: null } // Reset manual range when toggling
    });
  };
  
  const increaseYRange = () => {
    // Get current range, either from manual setting or auto-calculated
    const currentMin = plot.yRange.min !== null ? plot.yRange.min : yDomain[0] as number;
    const currentMax = plot.yRange.max !== null ? plot.yRange.max : yDomain[1] as number;
    const range = currentMax - currentMin;
    const center = (currentMin + currentMax) / 2;

    // Expand range by 50% (zoom out)
    const newRange = range * 1.5;
    updatePlot(plot.id, {
      yRange: {
        min: center - newRange / 2,
        max: center + newRange / 2
      }
    });
  };

  const decreaseYRange = () => {
    // Get current range, either from manual setting or auto-calculated
    const currentMin = plot.yRange.min !== null ? plot.yRange.min : yDomain[0] as number;
    const currentMax = plot.yRange.max !== null ? plot.yRange.max : yDomain[1] as number;
    const range = currentMax - currentMin;
    const center = (currentMin + currentMax) / 2;

    // Contract range by 33% (zoom in)
    const newRange = range * 0.67;
    updatePlot(plot.id, {
      yRange: {
        min: center - newRange / 2,
        max: center + newRange / 2
      }
    });
  };
  
  return (
    <div className="h-full w-full flex">
      {/* Vertical plot title on the left */}
      <div className="flex-shrink-0 flex items-center justify-center w-8">
        <h3
          className="text-md font-semibold transform -rotate-90 whitespace-nowrap"
          style={{ transformOrigin: 'center' }}
        >
          F<sub>{plot.title.slice(1)}</sub>
        </h3>
      </div>

      {/* Plot area with overlaid controls */}
      <div className="flex-1 relative">
        {/* Overlaid range controls at top right */}
        <div className="absolute top-2 right-2 z-10 flex space-x-1 bg-background/80 backdrop-blur-sm rounded-md p-1 border">
          <Button variant="ghost" size="sm" onClick={decreaseYRange} className="h-6 w-6 p-0">
            <ArrowDown className="h-3 w-3" />
          </Button>
          <Button
            variant={plot.fitToHistory ? "default" : "ghost"}
            size="sm"
            onClick={toggleFitMode}
            className="h-6 w-6 p-0"
            title={plot.fitToHistory ? "Fit to all data history" : "Auto-fit to current view"}
          >
            <Maximize2 className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" onClick={increaseYRange} className="h-6 w-6 p-0">
            <ArrowUp className="h-3 w-3" />
          </Button>
        </div>

        <div
          className="h-full w-full min-h-[120px]"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 15, right: 15, left: 5, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
              <XAxis
                dataKey="timestamp"
                type="number"
                domain={['dataMin', 'dataMax']}
                tickFormatter={(value) => new Date(value).toLocaleTimeString().split(':').slice(1).join(':')}
                tick={{ fontSize: 10 }}
              />
              <YAxis
                domain={yDomain}
                tick={{ fontSize: 10 }}
                tickFormatter={(value) => typeof value === 'number' ? value.toFixed(2) : value}
              />
              <ChartTooltip content={<ChartTooltipContent />} />

              {plot.signals.map((signal) => {
                if (!signal.visible) return null;
                return (
                  <Line
                    key={`${signal.plateId}-${signal.channel}`}
                    type="monotone"
                    dataKey={`${signal.plateId}-${signal.channel}`}
                    name={signal.label}
                    stroke={signal.color}
                    dot={false}
                    activeDot={{ r: 4 }}
                    isAnimationActive={false}
                  />
                );
              })}

              {plot.signals.some(s => s.visible) && plot.showSum && (
                <Line
                  key="sum"
                  type="monotone"
                  dataKey="sum"
                  name="Sum"
                  stroke="#fff"
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 4 }}
                  isAnimationActive={false}
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

const ForcePlots: React.FC = () => {
  const { forcePlates } = useSerial();
  const [plots, setPlots] = useState<PlotConfig[]>([...DEFAULT_PLOTS]);

  // Chart config for ChartContainer
  const chartConfig = useMemo(() => {
    const config: Record<string, { label: string; color: string }> = {};
    plots.forEach(plot => {
      plot.signals.forEach(signal => {
        config[`${signal.plateId}-${signal.channel}`] = {
          label: signal.label,
          color: signal.color,
        };
      });
      if (plot.showSum) {
        config['sum'] = { label: 'Sum', color: '#fff' };
      }
    });
    return config;
  }, [plots]);

  // Update plots when force plates change
  useEffect(() => {
    if (forcePlates.length === 0) return;

    // Use functional update to avoid dependency on plots state
    setPlots(currentPlots => {
      // Create a new plots array with updated signal configurations
      const newPlots = currentPlots.map(plot => {
        // Get all currently configured plate IDs
        const configuredPlateIds = new Set(plot.signals.map(s => s.plateId));
        // Add new signals for any plates not yet configured
        const newSignals = [...plot.signals];
        // Extract channel from plot title (assumed to be "Fx" => "fx", etc.)
        const channel = plot.title.slice(-1).toLowerCase() as 'x' | 'y' | 'z';
        const signalType = `f${channel}` as 'fx' | 'fy' | 'fz';

        // Check if we need to add any new signals
        let hasNewSignals = false;
        forcePlates.forEach((plate, i) => {
          if (!configuredPlateIds.has(plate.id)) {
            newSignals.push({
              plateId: plate.id,
              channel: signalType,
              visible: true,
              color: CHART_COLORS[i % CHART_COLORS.length],
              label: `${plate.name} ${signalType.toUpperCase()}`
            });
            hasNewSignals = true;
          }
        });

        return hasNewSignals ? {
          ...plot,
          signals: newSignals
        } : plot;
      });

      // Only update if there are actual changes
      const hasChanges = newPlots.some((plot, index) =>
        plot.signals.length !== currentPlots[index].signals.length
      );

      return hasChanges ? newPlots : currentPlots;
    });
  }, [forcePlates]);

  const updatePlot = (id: string, changes: Partial<PlotConfig>) => {
    setPlots(currentPlots => 
      currentPlots.map(plot => 
        plot.id === id ? { ...plot, ...changes } : plot
      )
    );
  };
  
  if (forcePlates.length === 0) {
    return null;
  }
  
  return (
    <div className="h-full w-full flex flex-col">
      <Card className="flex-1 min-h-0 w-full">
        <CardContent className="px-0 pb-0 h-full w-full">
          <ChartContainer config={chartConfig} className="h-full w-full">
            <ResizablePanelGroup direction="vertical" className="h-full w-full">
              {plots.map((plot, index) => (
                <React.Fragment key={plot.id}>
                  {index > 0 && <ResizableHandle withHandle />}
                  <ResizablePanel defaultSize={100 / plots.length} minSize={15} className="w-full">
                    <ForcePlot
                      plot={plot}
                      forcePlates={forcePlates}
                      updatePlot={updatePlot}
                    />
                  </ResizablePanel>
                </React.Fragment>
              ))}
            </ResizablePanelGroup>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
};

export default ForcePlots;
